// services/openai.js
const functions = require('firebase-functions');
const OpenAI = require('openai');
const openai = new OpenAI({
  apiKey: "********************************************************************************************************************************************************************"
});

const SYSTEM_PROMPT = `You are a farm assistant AI integrated into a mobile app. Your job is to understand user messages (text or image), identify their intent, and respond with a structured JSON-only response to power user actions.

The assistant supports the following entities:

- **Plant**: Can be added to a field or garden. Includes name, species, variety, status,healthStatus, planting date, etc.
- **Crop**: Added to a field only. A field can have only one active crop at a time.
- **Field**: Represents cropland. Includes name, type, size, status, and caretakers.
- **Garden**: A growing zone for plants. Includes garden type, soil type, irrigation, size, and location.
- **Animal**: Includes species, breed, gender, purpose, acquisition date, and health status.
- **Task**: Scheduled work like health checks, watering, or harvesting. Supports recurrence and checklist.
- **Equipment**: Tools or machinery linked to farms.
- **Lookups**: Predefined lists for species for Animals, breeds for Animals, status for plant Status, Health Status for plant and animlas health, types, statuses, etc.

---

### Your JSON Response Structure:


{
  "summary": "Brief explanation of what the user asked for.",
   "entityData": {
        "key1": "value1",
        "key2": "value2",
        "...": "..."
      },
  "suggestedActions": [
    {
      "type": "action_type_identifier",
      "label": "Button label for user",
      "entityType": "plant | crop | task | field | animal | garden | equipment",
    }
  ]
}`

async function analyzeMessage(text, imageUrl) {
  const messages = [
    {
      role: 'system',
      content: SYSTEM_PROMPT,
    }];

  const userContent = [];
  if (text) userContent.push({ type: 'text', text });
  if (imageUrl) userContent.push({ type: 'image_url', image_url: { url: imageUrl } });

  messages.push({ role: 'user', content: userContent });

  const response = await openai.chat.completions.create({
    model: 'gpt-4o',
    messages,
    temperature: 0.3
  });

  return response.choices[0].message.content;
}

// Enhanced function to extract specific entity data
async function extractEntityData(text, imageUrl, entityType) {
  const enhancedPrompt = `${SYSTEM_PROMPT}

IMPORTANT: The user is specifically trying to add a new ${entityType}. Please extract all relevant details for a ${entityType} from their message and image. Focus on extracting specific fields needed for this entity type.

For a ${entityType}, make sure to include these fields in your entityData:
${getEntityFields(entityType)}

Your response should prioritize extracting these specific fields from the user's input.`;

  const messages = [
    {
      role: 'system',
      content: enhancedPrompt,
    }
  ];

  const userContent = [];
  if (text) userContent.push({ type: 'text', text });
  if (imageUrl) userContent.push({ type: 'image_url', image_url: { url: imageUrl } });

  messages.push({ role: 'user', content: userContent });

  const response = await openai.chat.completions.create({
    model: 'gpt-4o',
    messages,
    temperature: 0.2
  });

  return response.choices[0].message.content;
}

// Helper function to get entity-specific fields
function getEntityFields(entityType) {
  switch (entityType) {
    case 'plant':
      return `- name: The plant's name
- species: Plant species (required)
- variety: Plant variety
- status: Growth stage (seedling, growing, mature, etc.)
- health: Health status (good, fair, poor)
- plantedDate: When the plant was planted
- expectedHarvestDate: When harvest is expected
- fieldId or gardenId: Where the plant is located
- notes: Any additional notes`;
      
    case 'animal':
      return `- name: The animal's name
- species: Animal species (required)
- breed: Animal breed
- gender: Animal gender (male, female, unknown)
- status: Health status (healthy, sick, etc.)
- purpose: Purpose (meat, dairy, etc.)
- birthDate: Birth or acquisition date
- fieldId: Where the animal is located
- notes: Any additional notes`;
      
    case 'field':
      return `- name: Field name
- type: Field type (cropland, pasture, etc.)
- size: Field size (number)
- sizeUnit: Unit of measurement (acres, hectares, etc.)
- status: Field status (active, fallow, etc.)
- location: Geographic location
- notes: Any additional notes`;
      
    case 'garden':
      return `- name: Garden name
- type: Garden type (vegetable, flower, herb, etc.)
- size: Garden size (number)
- sizeUnit: Unit of measurement (square_meters, square_feet, etc.)
- soilType: Type of soil
- irrigationSystem: Irrigation system type
- status: Garden status (active, dormant, etc.)
- location: Geographic location
- notes: Any additional notes`;
      
    case 'crop':
      return `- cropType: Type of crop
- plantedDate: When the crop was planted
- expectedHarvestDate: When harvest is expected
- soilType: Type of soil
- notes: Any additional notes`;
      
    default:
      return '';
  }
}

module.exports = { 
  analyzeMessage,
  extractEntityData
module.exports = { analyzeMessage };
