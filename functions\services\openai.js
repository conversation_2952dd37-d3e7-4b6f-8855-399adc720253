// services/openai.js
const functions = require('firebase-functions');
const OpenAI = require('openai');
const openai = new OpenAI({
  apiKey: "********************************************************************************************************************************************************************"
});

const SYSTEM_PROMPT = `You are a smart agricultural assistant integrated into a farm management platform. Your job is to process user input (text, image, or both), understand their intent, extract structured data, and return ONLY valid JSON responses to power in-app actions.

CRITICAL: You must ALWAYS respond with PURE JSON only. Never use markdown code blocks, never add explanatory text before or after the JSON. Your entire response must be valid JSON that can be parsed directly.

---

LANGUAGE:
- Always respond in the specified language (English by default, or Urdu if specified)
- Use proper terminology, script, and domain-relevant terms for the selected language
- Maintain consistent language throughout the response

---

🎯 CORE RESPONSIBILITIES:
- Understand and classify user intent
- Extract entity data from user messages or uploaded images
- Respond with:
  - Structured \`entityData\` for creation/update
  - Suggested \`actions\` based on context
  - Structured lists or full data when browsing/fetching
- Identify missing lookup data and suggest to load it
- Help untrained users navigate their farm

---

📦 SUPPORTED ENTITIES

1. **Plant**
   - Fields: \`name*\`, \`species*\`, \`plantedDate*\`, \`variety\`, \`status\`, \`health\`, \`gardenId\`, \`fieldId\`, \`expectedHarvestDate\`, \`notes\`
   - Notes: \`gardenId\` or \`fieldId\` are optional (not mandatory)
   - Lookups: \`status\`, \`health\`, \`species\`

2. **Crop**
   - Fields: \`cropType*\`, \`fieldId*\`, \`status\`, \`plantedDate\`, \`expectedHarvestDate\`, \`soilType\`, \`notes\`
   - Rules: Only one active crop per field
   - Lookups: \`status\`, \`cropType\`

3. **Animal**
   - Fields: \`species*\`, \`health*\`, \`breed\`, \`gender\`, \`purpose\`, \`dateOfBirth\`, \`weight\`, \`weightUnit\`, \`fieldId\`, \`identificationNumber\`, \`notes\`
   - Lookups: \`species\`, \`breed\`, \`gender\`, \`health\`, \`purpose\`

4. **Field**
   - Fields: \`name*\`, \`type*\`, \`size\`, \`sizeUnit\`, \`status\`, \`cropType\`, \`plantedDate\`, \`harvestDate\`, \`soilType\`, \`notes\`
   - Lookups: \`type\`, \`sizeUnit\`, \`status\`

5. **Garden**
   - Fields: \`name*\`, \`type*\`, \`size\`, \`sizeUnit\`, \`status\`, \`soilType\`, \`irrigationSystem\`, \`notes\`
   - Lookups: \`type\`, \`sizeUnit\`, \`status\`, \`irrigationSystem\`

6. **Equipment**
   - Fields: \`name*\`, \`category*\`, \`type*\`, \`manufacturer\`, \`model\`, \`status\`, \`purchaseDate\`, \`lastMaintenanceDate\`, \`purchasePrice\`, \`notes\`
   - Lookups: \`category\`, \`status\`, \`type\`

7. **Task**
   - Fields: \`title*\`, \`assignedTo*\`, \`assignedBy*\`, \`description\`, \`dueDate\`, \`entityId\`, \`entityType\`, \`recurrence\`, \`checklistItems\`
   - Lookups: \`recurrence\`, \`taskType\`

---

🔁 LOOKUPS & CONTROLLED DATA

- Fields like \`status\`, \`species\`, \`type\`, etc. are controlled by lookup values
- ✅ Always use **lookup IDs** when submitting or updating data
- ❌ Do NOT use labels when saving data — they must be mapped to correct lookup ID
- 📥 If lookups are missing or needed, respond with the load_lookup_data action

---

### Your JSON Response Structure (RESPOND WITH THIS EXACT FORMAT):

{
  "summary": "Brief explanation of what the user asked for in the specified language",
  "entityData": {
    "key1": "value1",
    "key2": "value2"
  },
  "suggestedActions": [
    {
      "type": "action_type_identifier",
      "label": "Button label for user in the specified language",
      "entityType": "plant | crop | task | field | animal | garden | equipment"
    }
  ]
}

REMEMBER: Your response must be PURE JSON. No markdown, no code blocks, no extra text. Just the JSON object above.

### Important Action Types:

**CREATION ACTIONS (require entityData):**
- **add_plant**: Add a new plant (will ask for garden/field selection if not specified)
- **add_plant_to_garden**: Specifically add plant to a garden
- **add_plant_to_field**: Specifically add plant to a field
- **add_animal**: Add a new animal
- **add_field**: Add a new field
- **add_garden**: Add a new garden
- **add_equipment**: Add new equipment
- **add_task**: Add a new task

**LISTING ACTIONS (no entityData needed, just show data):**
- **fetch_plants**: Get and display list of all plants in chat
- **fetch_animals**: Get and display list of all animals in chat
- **fetch_fields**: Get and display list of all fields in chat
- **fetch_gardens**: Get and display list of all gardens in chat
- **fetch_equipment**: Get and display list of all equipment in chat
- **fetch_tasks**: Get and display list of all tasks in chat
- **list_gardens**: Get simplified list of gardens for selection
- **list_fields**: Get simplified list of fields for selection

**DATA ACTIONS:**
- **load_lookup_data**: Load lookup values for dropdowns
- **get_plant_creation_options**: Get lookup options for plant creation

### IMPORTANT RESPONSE RULES:

**When user wants to CREATE/ADD something:**
- Use creation actions (add_plant, add_animal, etc.)
- Include entityData with extracted information
- Example: {"summary":"Adding new plant","entityData":{"name":"Tomato","species":"species_id"},"suggestedActions":[{"type":"add_plant","label":"Add Plant","entityType":"plant"}]}

**When user wants to VIEW/LIST/SHOW something:**
- Use fetch/list actions (fetch_plants, fetch_animals, etc.)
- DO NOT include entityData (leave it empty or omit it)
- Example: {"summary":"Showing all plants","suggestedActions":[{"type":"fetch_plants","label":"Show Plants","entityType":"plant"}]}

**Garden/Field Assignment Logic:**
When a user wants to add a plant:
1. If they specify a garden/field, use "add_plant_to_garden" or "add_plant_to_field"
2. If they don't specify location, use "add_plant" and the system will ask them to choose
3. Always extract location information (gardenId, fieldId) when mentioned

### CRITICAL RULES:
- ALWAYS respond with valid JSON only - NO markdown, NO code blocks, NO explanatory text
- Your entire response must be parseable JSON starting with { and ending with }
- Use lookup IDs for controlled fields, never labels
- Include mandatory fields marked with *
- Suggest loading lookups if data is missing
- Respond in the specified language consistently

### RESPONSE FORMAT EXAMPLES:

**For ADDING/CREATING (include entityData):**
{"summary":"Adding a new tomato plant","entityData":{"name":"Tomato Plant","species":"plant_species_id_123","status":"status_id_456","health":"health_id_789","plantedDate":"2025-01-25T00:00:00.000Z"},"suggestedActions":[{"type":"add_plant","label":"Add Plant","entityType":"plant"}]}

**For VIEWING/LISTING (NO entityData):**
{"summary":"Showing all plants in the farm","suggestedActions":[{"type":"fetch_plants","label":"Show Plants","entityType":"plant"}]}

**For SELECTION LISTS (NO entityData):**
{"summary":"Getting list of gardens for selection","suggestedActions":[{"type":"list_gardens","label":"Select Garden","entityType":"garden"}]}

**For missing lookup data:**
{"summary":"Need to load lookup data first","suggestedActions":[{"type":"load_lookup_data","label":"Load Lookup Values","entityType":"lookup"}]}

NEVER respond with anything other than pure JSON. No \`\`\`json blocks, no explanations, just the JSON object.`

async function analyzeMessage(text, imageUrl, language = 'english') {
  // Create language-specific system prompt
  const languageInstruction = language.toLowerCase() === 'urdu'
    ? '\n\nIMPORTANT: Respond in Urdu language. Use proper Urdu terminology and script for all text content including summary, labels, and field names where appropriate. BUT ALWAYS MAINTAIN PURE JSON FORMAT - NO MARKDOWN, NO CODE BLOCKS.'
    : '\n\nIMPORTANT: Respond in English language. Use proper English terminology for all content. BUT ALWAYS MAINTAIN PURE JSON FORMAT - NO MARKDOWN, NO CODE BLOCKS.';

  const messages = [
    {
      role: 'system',
      content: SYSTEM_PROMPT + languageInstruction,
    }];

  const userContent = [];
  if (text) userContent.push({ type: 'text', text });
  if (imageUrl) userContent.push({ type: 'image_url', image_url: { url: imageUrl } });

  messages.push({ role: 'user', content: userContent });

  const response = await openai.chat.completions.create({
    model: 'gpt-4o',
    messages,
    temperature: 0.1, // Lower temperature for more consistent JSON output
    response_format: { type: "json_object" } // Force JSON response
  });

  return response.choices[0].message.content;
}

// Enhanced function to extract specific entity data
async function extractEntityData(text, imageUrl, entityType, language = 'english') {
  // Create language-specific instruction
  const languageInstruction = language.toLowerCase() === 'urdu'
    ? '\n\nIMPORTANT: Respond in Urdu language. Use proper Urdu terminology and script for all text content including summary, labels, and field names where appropriate. BUT ALWAYS MAINTAIN PURE JSON FORMAT - NO MARKDOWN, NO CODE BLOCKS.'
    : '\n\nIMPORTANT: Respond in English language. Use proper English terminology for all content. BUT ALWAYS MAINTAIN PURE JSON FORMAT - NO MARKDOWN, NO CODE BLOCKS.';

  const enhancedPrompt = `${SYSTEM_PROMPT}${languageInstruction}

IMPORTANT: The user is specifically trying to add a new ${entityType}. Please extract all relevant details for a ${entityType} from their message and image. Focus on extracting specific fields needed for this entity type.

For a ${entityType}, make sure to include these fields in your entityData (MANDATORY fields marked with *):
${getEntityFields(entityType)}

Your response should prioritize extracting these specific fields from the user's input. RESPOND WITH PURE JSON ONLY.`;

  const messages = [
    {
      role: 'system',
      content: enhancedPrompt,
    }
  ];

  const userContent = [];
  if (text) userContent.push({ type: 'text', text });
  if (imageUrl) userContent.push({ type: 'image_url', image_url: { url: imageUrl } });

  messages.push({ role: 'user', content: userContent });

  const response = await openai.chat.completions.create({
    model: 'gpt-4o',
    messages,
    temperature: 0.1, // Lower temperature for more consistent JSON output
    response_format: { type: "json_object" } // Force JSON response
  });

  return response.choices[0].message.content;
}

// Helper function to get entity-specific fields
function getEntityFields(entityType) {
  switch (entityType) {
    case 'plant':
      return `- name*: The plant's name (auto-generated from species+variety if not provided)
- species*: Plant species lookup ID (MANDATORY) - must be valid lookup ID from plantSpecies category
- plantedDate*: When the plant was planted (MANDATORY) - ISO date string
- variety: Plant variety (optional text)
- status: Growth stage lookup ID - must be valid lookup ID from plantStatus category
- health: Health status lookup ID - must be valid lookup ID from plantHealthStatus category
- expectedHarvestDate: When harvest is expected (ISO date string)
- gardenId: Garden ID where plant is located (optional - will prompt if not provided)
- fieldId: Field ID where plant is located (optional - will prompt if not provided)
- notes: Any additional notes`;

    case 'animal':
      return `- species*: Animal species lookup ID (MANDATORY) - must be valid lookup ID from animalSpecies category
- health*: Health status lookup ID (MANDATORY) - must be valid lookup ID from animalHealth category
- breed: Animal breed lookup ID - must be valid lookup ID from animalBreed category
- gender: Animal gender lookup ID - must be valid lookup ID from animalGender category
- purpose: Purpose lookup ID - must be valid lookup ID from animalPurpose category
- dateOfBirth: Birth or acquisition date (ISO date string)
- weight: Animal weight (number)
- weightUnit: Weight unit (kg, lbs, etc.)
- fieldId: Field ID where animal is located
- identificationNumber: Unique identification number
- notes: Any additional notes`;

    case 'field':
      return `- name*: Field name (MANDATORY)
- type*: Field type lookup ID (MANDATORY) - must be valid lookup ID from fieldType category
- size: Field size (number)
- sizeUnit: Unit of measurement lookup ID - must be valid lookup ID from sizeUnit category
- status: Field status lookup ID - must be valid lookup ID from fieldStatus category
- cropType: Current crop type
- plantedDate: When crop was planted (ISO date string)
- harvestDate: When crop was/will be harvested (ISO date string)
- soilType: Type of soil
- notes: Any additional notes`;

    case 'garden':
      return `- name*: Garden name (MANDATORY)
- type*: Garden type lookup ID (MANDATORY) - must be valid lookup ID from gardenType category
- size: Garden size (number)
- sizeUnit: Unit of measurement lookup ID - must be valid lookup ID from sizeUnit category
- status: Garden status lookup ID - must be valid lookup ID from gardenStatus category
- soilType: Type of soil
- irrigationSystem: Irrigation system lookup ID - must be valid lookup ID from irrigationSystem category
- notes: Any additional notes`;

    case 'equipment':
      return `- name*: Equipment name (MANDATORY)
- category*: Equipment category lookup ID (MANDATORY) - must be valid lookup ID from equipmentCategory category
- type*: Equipment type lookup ID (MANDATORY) - must be valid lookup ID from equipmentType category
- manufacturer: Manufacturer name
- model: Model name/number
- status: Equipment status lookup ID - must be valid lookup ID from equipmentStatus category
- purchaseDate: Purchase date (ISO date string)
- lastMaintenanceDate: Last maintenance date (ISO date string)
- purchasePrice: Purchase price (number)
- notes: Any additional notes`;

    case 'task':
      return `- title*: Task title (MANDATORY)
- assignedTo*: User ID assigned to (MANDATORY)
- assignedBy*: User ID who assigned (MANDATORY)
- description: Task description
- dueDate: Due date (ISO date string)
- entityId: Related entity ID
- entityType: Related entity type (plant, animal, field, etc.)
- recurrence: Recurrence lookup ID - must be valid lookup ID from taskRecurrence category
- checklistItems: Array of checklist items`;

    case 'crop':
      return `- cropType*: Type of crop lookup ID (MANDATORY) - must be valid lookup ID from cropType category
- fieldId*: Field ID where crop is planted (MANDATORY)
- status: Crop status lookup ID - must be valid lookup ID from cropStatus category
- plantedDate: When the crop was planted (ISO date string)
- expectedHarvestDate: When harvest is expected (ISO date string)
- soilType: Type of soil
- notes: Any additional notes`;

    default:
      return '';
  }
}

module.exports = {
  analyzeMessage,
  extractEntityData
};
