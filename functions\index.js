// const functions = require("firebase-functions");
// const expressApp = require("./expressApp");
// const { onRequest } = require("firebase-functions/v2/https");

// exports.api = onRequest(expressApp);


// const functions = require('firebase-functions');
// const app = require('./expressApp');

// // Export your Express app as a Firebase Function
// exports.api = functions
//   .region('us-central1')
//   .https.onRequest(app);


const { onRequest } = require('firebase-functions/v2/https');
const app = require('./expressApp');

exports.api = onRequest(
  {
    region: 'us-central1',
    memory: '1GB',
    timeoutSeconds: 60,
    cpu: 1,
    minInstances: 0,
    maxInstances: 5,
  },
  app
);
