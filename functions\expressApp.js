// const express = require('express');
// const cors = require('cors');
// require('dotenv').config();

// const app = express();
// app.use(cors());
// app.use(express.json());

// const chatRoutes = require('./routes/chat');
// app.use('/chat', chatRoutes);

// const PORT = process.env.PORT || 5000;
// app.listen(PORT, () => console.log(`MCP server running on port ${PORT}`));


// const express = require('express');
// const cors = require('cors');
// require('dotenv').config();

// const app = express();

// app.use(cors());
// app.use(express.json());

// const chatRoutes = require('./routes/chat');
// app.use('/chat', chatRoutes);

// // ❌ REMOVE this line — do not listen to a port inside expressApp
// // const PORT = process.env.PORT || 5000;
// // app.listen(PORT, () => console.log(`Server running on port ${PORT}`));

// // ✅ EXPORT the app so Firebase Functions can handle it
// module.exports = app;


// expressApp.js
const express = require('express');
const cors = require('cors');
require('dotenv').config();

const app = express();
app.use(cors());
app.use(express.json());

const chatRoutes = require('./routes/chat');
app.use('/chat', chatRoutes);
app.use(express.json({ limit: '10mb' })); // increase limit for base64
// ❌ REMOVE app.listen()

module.exports = app;
