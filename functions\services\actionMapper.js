// services/actionMapper.js

/**
 * Maps AI suggestions to concrete actions with proper entity types
 * @param {Object} suggestion - The suggestion from the AI
 * @returns {Object} Mapped action with proper parameters
 */
function mapSuggestionToAction(suggestion) {
  const { type, entityType, entityData } = suggestion;
  
  // Default mapping
  const actionMap = {
    // Plant actions
    'add_plant': {
      type: 'add_plant',
      entityType: 'plant'
    },
    'check_plant_health': {
      type: 'health_check',
      entityType: 'plant'
    },
    'view_watering_schedule': {
      type: 'view_schedule',
      entityType: 'plant'
    },
    
    // Animal actions
    'add_animal': {
      type: 'add_animal',
      entityType: 'animal'
    },
    'check_animal_health': {
      type: 'health_check',
      entityType: 'animal'
    },
    
    // Field actions
    'add_field': {
      type: 'add_field',
      entityType: 'field'
    },
    'add_crop_to_field': {
      type: 'add_crop_to_field',
      entityType: 'crop'
    },
    
    // Garden actions
    'add_garden': {
      type: 'add_garden',
      entityType: 'garden'
    },
    
    // Lookup actions
    'get_plant_species': {
      type: 'get_lookups',
      category: 'plantSpecies'
    },
    'get_plant_status': {
      type: 'get_lookups',
      category: 'plantStatus'
    },
    'get_plant_health': {
      type: 'get_lookups',
      category: 'plantHealthStatus'
    },
    'get_animal_species': {
      type: 'get_lookups',
      category: 'animalSpecies'
    },
    'get_animal_breeds': {
      type: 'get_lookups',
      category: 'animalBreed'
    },
    'get_field_types': {
      type: 'get_lookups',
      category: 'fieldType'
    },
    'get_garden_types': {
      type: 'get_lookups',
      category: 'gardenType'
    },
    'get_soil_types': {
      type: 'get_lookups',
      category: 'soilType'
    }
  };
  
  // Get the mapped action or use the original
  const mappedAction = actionMap[type] || { type, entityType };
  
  // Add entity data if provided
  if (entityData) {
    mappedAction.entityData = entityData;
  }
  
  return mappedAction;
}

/**
 * Validates entity data against expected schema
 * @param {string} entityType - Type of entity
 * @param {Object} data - Entity data to validate
 * @returns {Object} Validation result with errors if any
 */
function validateEntityData(entityType, data) {
  const errors = [];
  
  switch (entityType) {
    case 'plant':
      if (!data.species) errors.push('Plant species is required');
      break;
      
    case 'animal':
      if (!data.species) errors.push('Animal species is required');
      break;
      
    case 'field':
      if (!data.size) errors.push('Field size is required');
      break;
      
    case 'garden':
      if (!data.size) errors.push('Garden size is required');
      break;
      
    case 'crop':
      if (!data.cropType) errors.push('Crop type is required');
      break;
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}

module.exports = {
  mapSuggestionToAction,
  validateEntityData
};