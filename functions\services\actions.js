const admin = require('./firebase');

// Existing functions
async function runHealthCheck(plantId) {
  // Dummy logic — later: analyze health history, images, etc.
  return {
    message: `Plant ${plantId} health check completed. No critical issues found.`
  };
}

async function getWateringSchedule(plantId) {
  return {
    message: `Next watering for plant ${plantId} is scheduled for 2025-07-25 at 10:00 AM.`
  };
}

async function searchSimilarIssues() {
  return {
    message: "We found 3 similar yellowing issues on tomato plants. Would you like to view them?"
  };
}

// New entity creation functions
async function addPlant(farmId, plantData) {
  try {
    const db = admin.firestore();
    const plantRef = db.collection(`farms/${farmId}/plants`).doc();
    
    const formattedPlantData = {
      name: plantData.name || 'New Plant',
      species: plantData.species,
      variety: plantData.variety || '',
      category: 'crop',
      quantity: 1,
      identificationID: plantData.identificationID,
      plantingDate: plantData.plantedDate ? new Date(plantData.plantedDate) : new Date(),
      expectedHarvestDate: plantData.expectedHarvestDate ? new Date(plantData.expectedHarvestDate) : null,
      growthStage: plantData.status || 'seedling',
      healthStatus: plantData.health || 'good',
      location: {
        zoneId: plantData.fieldId || plantData.gardenId || ''
      },
      helpGuide: plantData.helpGuide || [],
      notes: plantData.notes || '',
      addedBy: plantData.userId || '',
      addedAt: new Date(),
      photoURL: plantData.image || null
    };

    await plantRef.set(formattedPlantData);
    return { id: plantRef.id, ...formattedPlantData };
  } catch (error) {
    console.error('Error adding plant:', error);
    throw error;
  }
}

async function addAnimal(farmId, animalData) {
  try {
    const db = admin.firestore();
    const animalRef = db.collection(`farms/${farmId}/animals`).doc();
    
    const formattedAnimalData = {
      name: animalData.name || '',
      category: 'animal',
      type: animalData.species || '',
      breed: animalData.breed || '',
      gender: animalData.gender || 'unknown',
      status: animalData.status || 'healthy',
      purpose: animalData.purpose || 'meat',
      identificationNumber: animalData.identificationNumber || '',
      quantity: 1,
      unitOfMeasure: 'head',
      acquisitionDate: animalData.birthDate ? new Date(animalData.birthDate) : new Date(),
      location: {
        zoneId: animalData.fieldId || ''
      },
      notes: animalData.notes || '',
      addedBy: animalData.userId || '',
      addedAt: new Date(),
      photoURL: animalData.image || null
    };

    await animalRef.set(formattedAnimalData);
    return { id: animalRef.id, ...formattedAnimalData };
  } catch (error) {
    console.error('Error adding animal:', error);
    throw error;
  }
}

async function addField(farmId, fieldData) {
  try {
    const db = admin.firestore();
    const fieldRef = db.collection(`farms/${farmId}/zones`).doc();
    
    const formattedFieldData = {
      name: fieldData.name || 'New Field',
      type: fieldData.type || 'cropland',
      size: fieldData.size || 0,
      sizeUnit: fieldData.sizeUnit || 'acres',
      status: fieldData.status || 'active',
      assignedCaretakers: [],
      createdAt: new Date(),
      createdBy: fieldData.userId || '',
      lastModified: new Date(),
      location: fieldData.location || null,
      photoURL: fieldData.image || null
    };

    await fieldRef.set(formattedFieldData);
    return { id: fieldRef.id, ...formattedFieldData };
  } catch (error) {
    console.error('Error adding field:', error);
    throw error;
  }
}

async function addGarden(farmId, gardenData) {
  try {
    const db = admin.firestore();
    const gardenRef = db.collection(`farms/${farmId}/zones`).doc();
    
    const formattedGardenData = {
      name: gardenData.name || 'New Garden',
      type: 'garden',
      size: gardenData.size || 0,
      gardenType: gardenData.type,
      sizeUnit: gardenData.sizeUnit || 'square_meters',
      status: gardenData.status || 'active',
      assignedCaretakers: [],
      createdAt: new Date(),
      createdBy: gardenData.userId || '',
      lastModified: new Date(),
      location: gardenData.location || null,
      soilType: gardenData.soilType || null,
      irrigationSystem: gardenData.irrigationSystem || null,
      photoURL: gardenData.image || null
    };

    await gardenRef.set(formattedGardenData);
    return { id: gardenRef.id, ...formattedGardenData };
  } catch (error) {
    console.error('Error adding garden:', error);
    throw error;
  }
}

async function addCropToField(farmId, fieldId, cropData) {
  try {
    const db = admin.firestore();
    
    // Check if there's already an active crop
    const cropsSnapshot = await db.collection(`farms/${farmId}/zones/${fieldId}/crops`)
      .where('status', '==', 'active')
      .get();
    
    if (!cropsSnapshot.empty) {
      throw new Error('This field already has an active crop. Please harvest it first.');
    }
    
    const cropRef = db.collection(`farms/${farmId}/zones/${fieldId}/crops`).doc();
    const cropId = cropRef.id;
    
    const formattedCropData = {
      id: cropId,
      fieldId: fieldId,
      cropType: cropData.cropType,
      status: 'active',
      plantedDate: cropData.plantedDate ? new Date(cropData.plantedDate).toISOString() : new Date().toISOString(),
      expectedHarvestDate: cropData.expectedHarvestDate ? new Date(cropData.expectedHarvestDate).toISOString() : null,
      notes: cropData.notes || '',
      images: cropData.images || [],
      soilType: cropData.soilType || null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    await cropRef.set(formattedCropData);
    
    // Update field with activeCropId
    await db.collection(`farms/${farmId}/zones`).doc(fieldId).update({
      activeCropId: cropId
    });
    
    return { id: cropId, ...formattedCropData };
  } catch (error) {
    console.error('Error adding crop to field:', error);
    throw error;
  }
}

// Lookup helper function
async function getLookupsByCategory(category) {
  try {
    const db = admin.firestore();
    const lookupsSnapshot = await db.collection('lookups')
      .where('category', '==', category)
      .get();
    
    const lookups = [];
    lookupsSnapshot.forEach(doc => {
      lookups.push({ id: doc.id, ...doc.data() });
    });
    
    return lookups;
  } catch (error) {
    console.error(`Error getting ${category} lookups:`, error);
    throw error;
  }
}

module.exports = {
  runHealthCheck,
  getWateringSchedule,
  searchSimilarIssues,
  addPlant,
  addAnimal,
  addField,
  addGarden,
  addCropToField,
  getLookupsByCategory
};
