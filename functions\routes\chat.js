// routes/chat.js
const express = require('express');
const router = express.Router();
const { analyzeMessage } = require('../services/openai');
const admin = require('../services/firebase');
const { uploadImage } = require('../services/storage');
const {
  runHealthCheck,
  getWateringSchedule,
  searchSimilarIssues,
  addPlant,
  addAnimal,
  addField,
  addGarden,
  addCropToField,
  getLookupsByCategory,
  getLookupById,
  validateLookupId,
  getPlantCreationOptions,
  loadLookupData,
  fetchPlants,
  fetchAnimals,
  fetchEquipment,
  fetchFields,
  fetchGardens,
  fetchTasks,
  listGardens,
  listFields,
  addPlantToGarden,
  formatEntityListForChat
} = require('../services/actions');

async function saveChatMessage(userId, message) {
  const ref = admin.firestore().collection("users").doc(userId).collection("messages");
  await ref.add({
    ...message,
    timestamp: new Date()
  });
}
const app = express();
app.use(express.json({ limit: '10mb' })); // Important!
router.post('/analyze', async (req, res) => {
  console.log((req.body))
  try {
    const { text, userId, imageBase64, imageMimeType, imageName, language } = req.body;

    // Default language to English if not provided
    const userLanguage = language || 'english';

    let imageUrl = null;

    if (imageBase64 && imageMimeType && imageName) {
      const base64Data = imageBase64.split(';base64,').pop(); // remove data:image/jpeg;base64,
      const buffer = Buffer.from(base64Data, 'base64');

      imageUrl = await uploadImage(buffer, imageName, imageMimeType);
    }

    const aiResponse = await analyzeMessage(text, imageUrl, userLanguage);

    let suggestions;
    try {
      // Clean the response in case it has markdown or extra text
      let cleanResponse = aiResponse.trim();

      // Remove markdown code blocks if present
      if (cleanResponse.startsWith('```json')) {
        cleanResponse = cleanResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanResponse.startsWith('```')) {
        cleanResponse = cleanResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }

      // Find JSON object if there's extra text
      const jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        cleanResponse = jsonMatch[0];
      }

      suggestions = JSON.parse(cleanResponse);
    } catch (parseError) {
      console.error('JSON Parse Error:', parseError);
      console.error('Raw AI Response:', aiResponse);

      // Fallback response
      suggestions = {
        summary: "I apologize, but I encountered an error processing your request. Please try again.",
        suggestedActions: [
          {
            type: "load_lookup_data",
            label: "Load Lookup Data",
            entityType: "lookup"
          }
        ]
      };
    }

    // Save the message for history
    if (userId) {
      await saveChatMessage(userId, {
        text,
        imageUrl,
        language: userLanguage,
        aiResponse: suggestions
      });
    }

    res.json({ suggestions, language: userLanguage });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Analysis failed', message: err.message });
  }
});


router.post('/action', async (req, res) => {
  const { userId, farmId, action, entityData } = req.body;

  if (!action || !action.type) {
    return res.status(400).json({ error: 'Invalid action' });
  }

  try {
    let result;

    switch (action.type) {
      case 'health_check':
        result = await runHealthCheck(action.entityId);
        break;

      case 'view_schedule':
        result = await getWateringSchedule(action.entityId);
        break;

      case 'search_similar_issues':
        result = await searchSimilarIssues();
        break;
        
      case 'add_plant':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        result = await addPlant(farmId, { ...entityData, userId });
        break;
        
      case 'add_animal':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        result = await addAnimal(farmId, { ...entityData, userId });
        break;
        
      case 'add_field':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        result = await addField(farmId, { ...entityData, userId });
        break;
        
      case 'add_garden':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        result = await addGarden(farmId, { ...entityData, userId });
        break;
        
      case 'add_crop_to_field':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        if (!action.fieldId) return res.status(400).json({ error: 'Field ID is required' });
        result = await addCropToField(farmId, action.fieldId, entityData);
        break;
        
      case 'get_lookups':
        if (!action.category) return res.status(400).json({ error: 'Lookup category is required' });
        result = await getLookupsByCategory(action.category);
        break;

      case 'get_plant_creation_options':
        result = await getPlantCreationOptions();
        break;

      case 'load_lookup_data':
        result = await loadLookupData();
        break;

      case 'fetch_plants':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        const plantsData = await fetchPlants(farmId);
        result = {
          actionType: 'fetch',
          entityType: 'plant',
          data: plantsData.plants,
          message: formatEntityListForChat(plantsData.plants, 'plant'),
          count: plantsData.plants ? plantsData.plants.length : 0,
          success: true
        };
        break;

      case 'fetch_animals':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        const animalsData = await fetchAnimals(farmId);
        result = {
          actionType: 'fetch',
          entityType: 'animal',
          data: animalsData.animals,
          message: formatEntityListForChat(animalsData.animals, 'animal'),
          count: animalsData.animals ? animalsData.animals.length : 0,
          success: true
        };
        break;

      case 'fetch_equipment':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        const equipmentData = await fetchEquipment(farmId);
        result = {
          actionType: 'fetch',
          entityType: 'equipment',
          data: equipmentData.equipment,
          message: formatEntityListForChat(equipmentData.equipment, 'equipment'),
          count: equipmentData.equipment ? equipmentData.equipment.length : 0,
          success: true
        };
        break;

      case 'fetch_fields':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        const fieldsData = await fetchFields(farmId);
        result = {
          actionType: 'fetch',
          entityType: 'field',
          data: fieldsData.fields,
          message: formatEntityListForChat(fieldsData.fields, 'field'),
          count: fieldsData.fields ? fieldsData.fields.length : 0,
          success: true
        };
        break;

      case 'fetch_gardens':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        const gardensData = await fetchGardens(farmId);
        result = {
          actionType: 'fetch',
          entityType: 'garden',
          data: gardensData.gardens,
          message: formatEntityListForChat(gardensData.gardens, 'garden'),
          count: gardensData.gardens ? gardensData.gardens.length : 0,
          success: true
        };
        break;

      case 'fetch_tasks':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        const tasksData = await fetchTasks(farmId);
        result = {
          actionType: 'fetch',
          entityType: 'task',
          data: tasksData.tasks,
          message: formatEntityListForChat(tasksData.tasks, 'task'),
          count: tasksData.tasks ? tasksData.tasks.length : 0,
          success: true
        };
        break;

      case 'list_gardens':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        const gardensList = await listGardens(farmId);
        result = {
          actionType: 'list',
          entityType: 'garden',
          data: gardensList.gardens,
          message: `Found ${gardensList.gardens ? gardensList.gardens.length : 0} gardens available for selection.`,
          count: gardensList.gardens ? gardensList.gardens.length : 0,
          success: true
        };
        break;

      case 'list_fields':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        const fieldsList = await listFields(farmId);
        result = {
          actionType: 'list',
          entityType: 'field',
          data: fieldsList.fields,
          message: `Found ${fieldsList.fields ? fieldsList.fields.length : 0} fields available for selection.`,
          count: fieldsList.fields ? fieldsList.fields.length : 0,
          success: true
        };
        break;

      case 'add_plant_to_garden':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        if (!action.gardenId && !entityData.gardenId) {
          // If no garden is specified, return list of gardens for selection
          const gardens = await listGardens(farmId);
          return res.json({
            requiresSelection: true,
            selectionType: 'garden',
            options: gardens.gardens,
            message: 'Please select a garden to add the plant to:',
            pendingAction: action,
            pendingEntityData: entityData
          });
        }
        const gardenId = action.gardenId || entityData.gardenId;
        result = await addPlantToGarden(farmId, { ...entityData, userId }, gardenId);
        break;

      case 'add_plant_to_field':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        if (!action.fieldId && !entityData.fieldId) {
          // If no field is specified, return list of fields for selection
          const fields = await listFields(farmId);
          return res.json({
            requiresSelection: true,
            selectionType: 'field',
            options: fields.fields,
            message: 'Please select a field to add the plant to:',
            pendingAction: action,
            pendingEntityData: entityData
          });
        }
        const fieldId = action.fieldId || entityData.fieldId;
        const plantDataWithField = { ...entityData, fieldId };
        result = await addPlant(farmId, { ...plantDataWithField, userId });
        break;

      default:
        result = { message: `No handler for action type: ${action.type}` };
    }

    res.json({ result });

  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Failed to handle action', message: err.message });
  }
});

// New endpoint to handle selection confirmations
router.post('/confirm-selection', async (req, res) => {
  const { userId, farmId, selectedId, pendingAction, pendingEntityData } = req.body;

  if (!selectedId || !pendingAction || !pendingEntityData) {
    return res.status(400).json({ error: 'Missing required selection data' });
  }

  try {
    let result;

    switch (pendingAction.type) {
      case 'add_plant_to_garden':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        result = await addPlantToGarden(farmId, { ...pendingEntityData, userId }, selectedId);
        break;

      case 'add_plant_to_field':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        // Update plant data with field location
        const plantDataWithField = { ...pendingEntityData, fieldId: selectedId };
        result = await addPlant(farmId, { ...plantDataWithField, userId });
        break;

      default:
        result = { message: `No handler for pending action type: ${pendingAction.type}` };
    }

    res.json({ result });

  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Failed to handle selection', message: err.message });
  }
});

// Test endpoint to verify API is working
router.get('/test', (req, res) => {
  res.json({
    message: 'Chat API is working correctly',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'chat-api',
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
