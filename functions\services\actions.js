const admin = require('./firebase');

// Existing functions
async function runHealthCheck(plantId) {
  // Dummy logic — later: analyze health history, images, etc.
  return {
    message: `Plant ${plantId} health check completed. No critical issues found.`
  };
}

async function getWateringSchedule(plantId) {
  return {
    message: `Next watering for plant ${plantId} is scheduled for 2025-07-25 at 10:00 AM.`
  };
}

async function searchSimilarIssues() {
  return {
    message: "We found 3 similar yellowing issues on tomato plants. Would you like to view them?"
  };
}

// Enhanced plant creation with validation and lookup handling
async function addPlant(farmId, plantData) {
  try {
    const db = admin.firestore();

    // Validate required fields
    if (!plantData.species) {
      throw new Error('Plant species is required');
    }
    if (!plantData.status) {
      throw new Error('Plant growth stage/status is required');
    }
    if (!plantData.health) {
      throw new Error('Plant health status is required');
    }

    // Validate lookup IDs if provided
    const validationPromises = [];
    if (plantData.species) {
      validationPromises.push(validateLookupId(plantData.species, 'plantSpecies'));
    }
    if (plantData.status) {
      validationPromises.push(validateLookupId(plantData.status, 'plantStatus'));
    }
    if (plantData.health) {
      validationPromises.push(validateLookupId(plantData.health, 'plantHealthStatus'));
    }

    // Wait for all validations to complete
    await Promise.all(validationPromises);

    // Generate plant name based on species and variety if not provided
    let plantName = plantData.name;
    if (!plantName && plantData.species) {
      const speciesLookup = await getLookupById(plantData.species);
      const speciesName = speciesLookup ? speciesLookup.title : 'Plant';
      const varietyName = plantData.variety ? ` (${plantData.variety})` : '';
      plantName = `${speciesName}${varietyName}`;
    }

    const plantRef = db.collection(`farms/${farmId}/plants`).doc();

    const formattedPlantData = {
      name: plantName || 'New Plant',
      species: plantData.species, // Store lookup ID
      variety: plantData.variety || '',
      category: 'crop',
      quantity: 1,
      identificationID: plantData.identificationID || '',
      plantingDate: plantData.plantedDate ? new Date(plantData.plantedDate) : new Date(),
      expectedHarvestDate: plantData.expectedHarvestDate ? new Date(plantData.expectedHarvestDate) : null,
      growthStage: plantData.status, // Store lookup ID
      healthStatus: plantData.health, // Store lookup ID
      location: {
        zoneId: plantData.fieldId || plantData.gardenId || '',
        zoneType: plantData.fieldId ? 'field' : 'garden'
      },
      helpGuide: plantData.helpGuide || [],
      notes: plantData.notes || '',
      addedBy: plantData.userId || '',
      addedAt: new Date(),
      photoURL: plantData.image || null,
      isInactive: false
    };

    await plantRef.set(formattedPlantData);

    // Return with lookup details for immediate use
    const [speciesLookup, statusLookup, healthLookup] = await Promise.all([
      getLookupById(plantData.species),
      getLookupById(plantData.status),
      getLookupById(plantData.health)
    ]);

    return {
      id: plantRef.id,
      ...formattedPlantData,
      speciesName: speciesLookup?.title || '',
      statusName: statusLookup?.title || '',
      healthName: healthLookup?.title || '',
      message: `Plant "${plantName}" added successfully`
    };
  } catch (error) {
    console.error('Error adding plant:', error);
    throw error;
  }
}

async function addAnimal(farmId, animalData) {
  try {
    const db = admin.firestore();
    const animalRef = db.collection(`farms/${farmId}/animals`).doc();
    
    const formattedAnimalData = {
      name: animalData.name || '',
      category: 'animal',
      type: animalData.species || '',
      breed: animalData.breed || '',
      gender: animalData.gender || 'unknown',
      status: animalData.status || 'healthy',
      purpose: animalData.purpose || 'meat',
      identificationNumber: animalData.identificationNumber || '',
      quantity: 1,
      unitOfMeasure: 'head',
      acquisitionDate: animalData.birthDate ? new Date(animalData.birthDate) : new Date(),
      location: {
        zoneId: animalData.fieldId || ''
      },
      notes: animalData.notes || '',
      addedBy: animalData.userId || '',
      addedAt: new Date(),
      photoURL: animalData.image || null
    };

    await animalRef.set(formattedAnimalData);
    return { id: animalRef.id, ...formattedAnimalData };
  } catch (error) {
    console.error('Error adding animal:', error);
    throw error;
  }
}

async function addField(farmId, fieldData) {
  try {
    const db = admin.firestore();
    const fieldRef = db.collection(`farms/${farmId}/zones`).doc();
    
    const formattedFieldData = {
      name: fieldData.name || 'New Field',
      type: fieldData.type || 'cropland',
      size: fieldData.size || 0,
      sizeUnit: fieldData.sizeUnit || 'acres',
      status: fieldData.status || 'active',
      assignedCaretakers: [],
      createdAt: new Date(),
      createdBy: fieldData.userId || '',
      lastModified: new Date(),
      location: fieldData.location || null,
      photoURL: fieldData.image || null
    };

    await fieldRef.set(formattedFieldData);
    return { id: fieldRef.id, ...formattedFieldData };
  } catch (error) {
    console.error('Error adding field:', error);
    throw error;
  }
}

async function addGarden(farmId, gardenData) {
  try {
    const db = admin.firestore();
    const gardenRef = db.collection(`farms/${farmId}/zones`).doc();
    
    const formattedGardenData = {
      name: gardenData.name || 'New Garden',
      type: 'garden',
      size: gardenData.size || 0,
      gardenType: gardenData.type,
      sizeUnit: gardenData.sizeUnit || 'square_meters',
      status: gardenData.status || 'active',
      assignedCaretakers: [],
      createdAt: new Date(),
      createdBy: gardenData.userId || '',
      lastModified: new Date(),
      location: gardenData.location || null,
      soilType: gardenData.soilType || null,
      irrigationSystem: gardenData.irrigationSystem || null,
      photoURL: gardenData.image || null
    };

    await gardenRef.set(formattedGardenData);
    return { id: gardenRef.id, ...formattedGardenData };
  } catch (error) {
    console.error('Error adding garden:', error);
    throw error;
  }
}

async function addCropToField(farmId, fieldId, cropData) {
  try {
    const db = admin.firestore();
    
    // Check if there's already an active crop
    const cropsSnapshot = await db.collection(`farms/${farmId}/zones/${fieldId}/crops`)
      .where('status', '==', 'active')
      .get();
    
    if (!cropsSnapshot.empty) {
      throw new Error('This field already has an active crop. Please harvest it first.');
    }
    
    const cropRef = db.collection(`farms/${farmId}/zones/${fieldId}/crops`).doc();
    const cropId = cropRef.id;
    
    const formattedCropData = {
      id: cropId,
      fieldId: fieldId,
      cropType: cropData.cropType,
      status: 'active',
      plantedDate: cropData.plantedDate ? new Date(cropData.plantedDate).toISOString() : new Date().toISOString(),
      expectedHarvestDate: cropData.expectedHarvestDate ? new Date(cropData.expectedHarvestDate).toISOString() : null,
      notes: cropData.notes || '',
      images: cropData.images || [],
      soilType: cropData.soilType || null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    await cropRef.set(formattedCropData);
    
    // Update field with activeCropId
    await db.collection(`farms/${farmId}/zones`).doc(fieldId).update({
      activeCropId: cropId
    });
    
    return { id: cropId, ...formattedCropData };
  } catch (error) {
    console.error('Error adding crop to field:', error);
    throw error;
  }
}

// Enhanced lookup functions
async function getLookupsByCategory(category) {
  try {
    const db = admin.firestore();
    const lookupsSnapshot = await db.collection('lookups')
      .where('category', '==', category)
      .get();

    const lookups = [];
    lookupsSnapshot.forEach(doc => {
      lookups.push({ id: doc.id, ...doc.data() });
    });

    return lookups;
  } catch (error) {
    console.error(`Error getting ${category} lookups:`, error);
    throw error;
  }
}

// Get lookup details by ID
async function getLookupById(lookupId) {
  try {
    const db = admin.firestore();
    const lookupDoc = await db.collection('lookups').doc(lookupId).get();

    if (lookupDoc.exists) {
      return { id: lookupDoc.id, ...lookupDoc.data() };
    }
    return null;
  } catch (error) {
    console.error(`Error getting lookup ${lookupId}:`, error);
    return null;
  }
}

// Get multiple lookups by IDs
async function getLookupsById(lookupIds) {
  try {
    const db = admin.firestore();
    const lookupMap = {};

    if (lookupIds && lookupIds.length > 0) {
      // Firestore 'in' queries are limited to 10 items
      for (let i = 0; i < lookupIds.length; i += 10) {
        const batchIds = lookupIds.slice(i, i + 10);
        const lookupsSnapshot = await db.collection('lookups')
          .where('__name__', 'in', batchIds)
          .get();

        lookupsSnapshot.forEach(doc => {
          lookupMap[doc.id] = { id: doc.id, ...doc.data() };
        });
      }
    }

    return lookupMap;
  } catch (error) {
    console.error('Error getting lookups by IDs:', error);
    return {};
  }
}

// Validate lookup ID exists in category
async function validateLookupId(lookupId, expectedCategory) {
  try {
    const lookup = await getLookupById(lookupId);
    if (!lookup) {
      throw new Error(`Lookup with ID ${lookupId} not found`);
    }
    if (expectedCategory && lookup.category !== expectedCategory) {
      throw new Error(`Lookup ${lookupId} is not in category ${expectedCategory}`);
    }
    return lookup;
  } catch (error) {
    console.error(`Validation error for lookup ${lookupId}:`, error);
    throw error;
  }
}

// Helper function to get lookup title locally (fallback)
function getLookupTitleLocal(lookupId) {
  // This would typically fetch from a local cache or lookup table
  // For now, return the ID as fallback
  return lookupId;
}

// Enhanced Fetch Plants with proper lookup resolution
async function fetchPlants(farmId) {
  try {
    const db = admin.firestore();
    const plantsQuery = db.collection(`farms/${farmId}/plants`);
    const plantsSnapshot = await plantsQuery.get();

    if (plantsSnapshot.empty) {
      return { plants: [], message: "No plants found in this farm." };
    }

    // Collect all unique lookup IDs
    const lookupIds = new Set();
    plantsSnapshot.docs.forEach(doc => {
      const data = doc.data();
      if (data.species) lookupIds.add(data.species);
      if (data.growthStage) lookupIds.add(data.growthStage);
      if (data.healthStatus) lookupIds.add(data.healthStatus);
    });

    // Fetch all lookups in batch
    const lookupMap = await getLookupsById(Array.from(lookupIds));

    // Create an array of promises for fetching health check data for each plant
    const plantPromises = plantsSnapshot.docs.map(async (doc) => {
      const data = doc.data();
      const plantId = doc.id;

      // Resolve lookup titles
      const speciesLookup = lookupMap[data.species];
      const statusLookup = lookupMap[data.growthStage];
      const healthLookup = lookupMap[data.healthStatus];

      // Fetch the latest health check data for this plant
      const healthCheckRef = db.collection(`farms/${farmId}/DailyPlantHealthCheck`);
      const healthCheckQuery = healthCheckRef.where('plantId', '==', plantId);
      const healthCheckSnapshot = await healthCheckQuery.get();
      let latestHealthCheck = null;

      if (!healthCheckSnapshot.empty) {
        latestHealthCheck = healthCheckSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
      }

      return {
        id: plantId,
        name: data.name,
        species: data.species || '',
        speciesName: speciesLookup?.title || '',
        variety: data.variety || undefined,
        plantedDate: data.plantingDate?.toDate?.() ? data.plantingDate.toDate().toISOString() : new Date().toISOString(),
        expectedHarvestDate: data.expectedHarvestDate || '',
        status: data.growthStage || '',
        statusName: statusLookup?.title || '',
        health: data.healthStatus || '',
        healthName: healthLookup?.title || '',
        gardenId: data.location?.zoneType === 'garden' ? data.location?.zoneId : undefined,
        fieldId: data.location?.zoneType === 'field' ? data.location?.zoneId : undefined,
        farmId: farmId,
        location: data?.location || undefined,
        notes: data.notes || undefined,
        image: data.photoURL || undefined,
        createdAt: data.addedAt?.toDate?.() ? data.addedAt.toDate().toISOString() : new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isInactive: data?.isInactive || false,
        inactiveReason: data?.inactiveReason || undefined,
        inactiveDate: data.inactiveDate?.toDate?.() ? data.inactiveDate.toDate().toISOString() : new Date().toISOString(),
        identificationID: data?.identificationID,
        healthCheck: latestHealthCheck,
        helpGuide: data?.helpGuide || undefined,
      };
    });

    const plantsWithHealthData = await Promise.all(plantPromises);
    return {
      plants: plantsWithHealthData,
      message: `Found ${plantsWithHealthData.length} plants in this farm.`
    };
  } catch (error) {
    console.error('Fetch plants error:', error);
    throw error;
  }
}

// Fetch Animals
async function fetchAnimals(farmId) {
  try {
    const db = admin.firestore();
    const animalsRef = db.collection('farms', farmId, 'animals');
    const animalSnapshots = await animalsRef.get();

    const animalPromises = animalSnapshots.docs.map(async (animalDoc) => {
      const animalData = animalDoc.data();
      const animalId = animalDoc.id;

      const [healthChecksSnap, pregnanciesSnap] = await Promise.all([
        db.collection('farms', farmId, 'animals', animalId, 'healthChecks').get(),
        db.collection('farms', farmId, 'animals', animalId, 'pregnancies').get()
      ]);

      const healthChecks = healthChecksSnap.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      }));

      const pregnancies = pregnanciesSnap.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      }));

      return {
        id: animalId,
        age: animalData?.age,
        name: animalData.name || undefined,
        species: animalData?.species || animalData.type || '',
        breed: animalData.breed || undefined,
        birthDate: animalData?.acquisitionDate?.toDate?.() ? animalData.acquisitionDate.toDate().toISOString() : undefined,
        gender: animalData?.gender || 'unknown',
        status: animalData?.status || 'healthy',
        purpose: animalData?.purpose || 'meat',
        identificationNumber: animalData?.identificationNumber || undefined,
        farmId: farmId,
        fieldId: animalData?.location?.zoneId || undefined,
        notes: animalData?.notes || undefined,
        image: animalData?.photoURL || animalData?.imageUri || undefined,
        createdAt: animalData?.addedAt?.toDate?.() ? animalData?.addedAt?.toDate()?.toISOString() : new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isInactive: animalData?.isInactive || false,
        inactiveReason: animalData?.inactiveReason || undefined,
        inactiveDate: animalData?.inactiveDate?.toDate?.() ? animalData?.inactiveDate?.toDate().toISOString() : new Date().toISOString(),
        healthChecks,
        pregnancies,
      };
    });

    const animals = await Promise.all(animalPromises);
    return { animals: animals };
  } catch (error) {
    console.error("Fetch animals error:", error);
    throw error;
  }
}

// Fetch Equipment
async function fetchEquipment(farmId) {
  try {
    const db = admin.firestore();
    const equipmentQuery = db.collection(`farms/${farmId}/machinery`);
    const equipmentSnapshot = await equipmentQuery.get();
    const equipmentData = [];

    equipmentSnapshot.forEach((doc) => {
      const data = doc.data();
      const type = getLookupTitleLocal(data?.type);
      equipmentData.push({
        id: doc.id,
        name: data.name,
        type: type || data.type || 'tool',
        fuelType: data.fuelType,
        manufacturer: data.manufacturer || undefined,
        model: data.model || undefined,
        purchaseDate: data.acquisitionDate?.toDate?.() ? data.acquisitionDate.toDate().toISOString() : undefined,
        purchasePrice: data.acquisitionCost || undefined,
        status: data.status || 'operational',
        lastMaintenanceDate: data.lastMaintenanceDate?.toDate?.() ? data.lastMaintenanceDate.toDate().toISOString() : undefined,
        nextMaintenanceDate: data.nextMaintenanceDate?.toDate?.() ? data.nextMaintenanceDate.toDate().toISOString() : undefined,
        farmId: farmId,
        location: data.location?.zoneId || undefined,
        notes: data.notes || undefined,
        image: data.photoURL || data?.imageUrl || undefined,
        createdAt: data.addedAt?.toDate?.() ? data.addedAt.toDate().toISOString() : new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isInactive: data?.isInactive || false,
        inactiveReason: data?.inactiveReason || undefined,
        inactiveDate: data.inactiveDate?.toDate?.() ? data.inactiveDate.toDate().toISOString() : new Date().toISOString(),
        currentFuelLevel: data?.currentFuelLevel || '',
        fuelType: data?.fuelType || '',
        photoUrl: data?.imageUrl
      });
    });

    return { equipment: equipmentData };
  } catch (error) {
    console.error('Fetch equipment error:', error);
    throw error;
  }
}

// Fetch Fields
async function fetchFields(farmId) {
  try {
    const db = admin.firestore();
    const zonesQuery = db.collection(`farms/${farmId}/zones`)
      .where('type', 'in', ['nSEVmRWD9OqffU6uwqPp']);
    const zonesSnapshot = await zonesQuery.get();
    const fieldsData = [];

    zonesSnapshot.forEach((doc) => {
      const data = doc.data();
      const sizeUnit = getLookupTitleLocal(data.sizeUnit);
      const type = getLookupTitleLocal(data.type);
      const status = getLookupTitleLocal(data.status);

      fieldsData.push({
        id: doc.id,
        name: data.name,
        type: type || data.type,
        size: data.size,
        sizeUnit: sizeUnit || data.sizeUnit,
        status: status || data.status,
        activeCropId: data?.activeCropId,
        plantedDate: data.plantedDate ?
          (data.plantedDate.toDate ? data.plantedDate.toDate().toISOString() : new Date(data.plantedDate).toISOString())
          : undefined,
        harvestDate: data.harvestDate ?
          (data.harvestDate.toDate ? data.harvestDate.toDate().toISOString() : new Date(data.harvestDate).toISOString())
          : undefined,
        health: data.health || undefined,
        farmId: farmId,
        location: data.location || undefined,
        image: data.photoURL || undefined,
      });
    });

    return { fields: fieldsData };
  } catch (error) {
    console.error('Fetch fields error:', error);
    throw error;
  }
}

// Fetch Gardens
async function fetchGardens(farmId) {
  try {
    const db = admin.firestore();
    const gardensQuery = db.collection(`farms/${farmId}/zones`)
      .where('type', '==', 'garden');
    const gardensSnapshot = await gardensQuery.get();
    const gardensData = [];

    gardensSnapshot.forEach((doc) => {
      const data = doc.data();
      const gardenType = getLookupTitleLocal(data.gardenType);
      const status = getLookupTitleLocal(data.status);
      const sizeUnit = getLookupTitleLocal(data.sizeUnit);
      const soilType = getLookupTitleLocal(data.soilType);
      const irrigationSystem = getLookupTitleLocal(data.irrigationSystem);

      gardensData.push({
        id: doc.id,
        name: data.name,
        type: data.type,
        gardenType: gardenType || data?.gardenType,
        size: data.size,
        sizeUnit: sizeUnit || data.sizeUnit,
        location: data.location || undefined,
        status: status || data.status,
        soilType: soilType || data.soilType || undefined,
        irrigationSystem: irrigationSystem || data.irrigationSystem || undefined,
        farmId: farmId,
        createdAt: data.createdAt?.toDate?.() ? data.createdAt.toDate().toISOString() : new Date().toISOString(),
        updatedAt: data.lastModified?.toDate?.() ? data.lastModified.toDate().toISOString() : new Date().toISOString(),
        image: data.photoURL || undefined,
        isInactive: data?.isInactive || false,
        inactiveReason: data?.inactiveReason || undefined,
        inactiveDate: data.inactiveDate?.toDate?.() ? data.inactiveDate.toDate().toISOString() : new Date().toISOString(),
      });
    });

    return { gardens: gardensData };
  } catch (error) {
    console.error('Fetch gardens error:', error);
    throw error;
  }
}

// Fetch Tasks
async function fetchTasks(farmId) {
  try {
    const db = admin.firestore();
    const tasksRef = db.collection(`farms/${farmId}/tasks`);
    const tasksSnapshot = await tasksRef.get();

    const tasksData = [];
    const checklistIds = new Set();

    tasksSnapshot.forEach((docSnap) => {
      const data = docSnap.data();
      const status = getLookupTitleLocal(data.status);
      const priority = getLookupTitleLocal(data.priority);
      const frequency = getLookupTitleLocal(data.frequency);

      const task = {
        id: docSnap.id,
        title: data.title,
        description: data.description || undefined,
        status: status || data.status,
        priority: priority || data.priority,
        dueDate: data.dueDate?.toDate?.() ? data.dueDate.toDate().toISOString() : new Date().toISOString(),
        assignedTo: data.assignedTo || undefined,
        assignedBy: data.createdBy || undefined,
        assignedToName: '',
        assignedByName: '',
        fieldId: data.location?.zoneId || undefined,
        farmId,
        createdAt: data.createdAt?.toDate?.() ? data.createdAt.toDate().toISOString() : new Date().toISOString(),
        updatedAt: data.updatedAt?.toDate?.() ? data.updatedAt.toDate().toISOString() : new Date().toISOString(),
        completedAt: data.completedDate?.toDate?.() ? data.completedDate.toDate().toISOString() : undefined,
        evidence: data.evidence || undefined,
        frequency: frequency || data?.frequency,
        assignedChecklistId: data.AssignedCheckListId || undefined,
        checklistDetails: data?.checklist || undefined,
        imageUrl: data?.photos,
        entityId: data?.entityId,
        relatedEntityType: data?.relatedEntityType,
      };

      if (data.AssignedCheckListId) {
        checklistIds.add(data.AssignedCheckListId);
      }

      tasksData.push(task);
    });

    const checklistIdArray = Array.from(checklistIds);
    const checklistMap = {};

    if (checklistIdArray.length > 0) {
      const checklistCollection = db.collection('checklists');

      for (let i = 0; i < checklistIdArray.length; i += 10) {
        const batchIds = checklistIdArray.slice(i, i + 10);
        const checklistQuery = checklistCollection.where('__name__', 'in', batchIds);
        const checklistSnapshot = await checklistQuery.get();

        checklistSnapshot.forEach((checklistDoc) => {
          checklistMap[checklistDoc.id] = {
            id: checklistDoc.id,
            ...checklistDoc.data(),
          };
        });
      }
    }

    // Attach checklistDetails to each task
    const enrichedTasks = tasksData?.map((task) => ({
      ...task,
      checklistDetails: task?.checklistDetails ? task.checklistDetails : task.assignedChecklistId
        ? checklistMap[task.assignedChecklistId]
        : undefined,
    }));

    return { tasks: enrichedTasks };
  } catch (error) {
    console.error('Fetch tasks error:', error);
    throw error;
  }
}

// Enhanced Add Plant to Garden with validation
async function addPlantToGarden(farmId, plantData, gardenId) {
  try {
    const db = admin.firestore();

    // Validate required fields
    if (!plantData.species) {
      throw new Error('Plant species is required');
    }
    if (!plantData.status) {
      throw new Error('Plant growth stage/status is required');
    }
    if (!plantData.health) {
      throw new Error('Plant health status is required');
    }

    // First check if the garden exists
    const gardenRef = db.collection(`farms/${farmId}/zones`).doc(gardenId);
    const gardenDoc = await gardenRef.get();

    if (!gardenDoc.exists) {
      throw new Error(`Garden with ID ${gardenId} does not exist`);
    }

    // Validate lookup IDs
    const validationPromises = [
      validateLookupId(plantData.species, 'plantSpecies'),
      validateLookupId(plantData.status, 'plantStatus'),
      validateLookupId(plantData.health, 'plantHealthStatus')
    ];

    await Promise.all(validationPromises);

    // Generate plant name based on species and variety if not provided
    let plantName = plantData.name;
    if (!plantName && plantData.species) {
      const speciesLookup = await getLookupById(plantData.species);
      const speciesName = speciesLookup ? speciesLookup.title : 'Plant';
      const varietyName = plantData.variety ? ` (${plantData.variety})` : '';
      plantName = `${speciesName}${varietyName}`;
    }

    // Create the plant with the garden location
    const plantRef = db.collection(`farms/${farmId}/plants`).doc();

    const formattedPlantData = {
      name: plantName || 'New Plant',
      species: plantData.species, // Store lookup ID
      variety: plantData.variety || '',
      category: 'crop',
      quantity: 1,
      identificationID: plantData.identificationID || '',
      plantingDate: plantData.plantedDate ? new Date(plantData.plantedDate) : new Date(),
      expectedHarvestDate: plantData.expectedHarvestDate ? new Date(plantData.expectedHarvestDate) : null,
      growthStage: plantData.status, // Store lookup ID
      healthStatus: plantData.health, // Store lookup ID
      location: {
        zoneId: gardenId,
        zoneType: 'garden'
      },
      helpGuide: plantData.helpGuide || [],
      notes: plantData.notes || '',
      addedBy: plantData.userId || '',
      addedAt: new Date(),
      photoURL: plantData.image || null,
      isInactive: false
    };

    await plantRef.set(formattedPlantData);

    // Return with lookup details for immediate use
    const [speciesLookup, statusLookup, healthLookup] = await Promise.all([
      getLookupById(plantData.species),
      getLookupById(plantData.status),
      getLookupById(plantData.health)
    ]);

    return {
      id: plantRef.id,
      ...formattedPlantData,
      gardenId,
      gardenName: gardenDoc.data().name,
      speciesName: speciesLookup?.title || '',
      statusName: statusLookup?.title || '',
      healthName: healthLookup?.title || '',
      message: `Plant "${plantName}" successfully added to garden "${gardenDoc.data().name}"`
    };
  } catch (error) {
    console.error('Error adding plant to garden:', error);
    throw error;
  }
}

// List Gardens for Selection
async function listGardens(farmId) {
  try {
    const db = admin.firestore();
    const gardensQuery = db.collection(`farms/${farmId}/zones`)
      .where('type', '==', 'garden')
      .where('status', '==', 'active');
    const gardensSnapshot = await gardensQuery.get();
    const gardens = [];

    gardensSnapshot.forEach((doc) => {
      const data = doc.data();
      gardens.push({
        id: doc.id,
        name: data.name,
        type: data.gardenType || 'garden',
        size: data.size,
        sizeUnit: data.sizeUnit
      });
    });

    return { gardens };
  } catch (error) {
    console.error('List gardens error:', error);
    throw error;
  }
}

// List Fields for Selection
async function listFields(farmId) {
  try {
    const db = admin.firestore();
    const fieldsQuery = db.collection(`farms/${farmId}/zones`)
      .where('type', 'in', ['nSEVmRWD9OqffU6uwqPp'])
      .where('status', '==', 'active');
    const fieldsSnapshot = await fieldsQuery.get();
    const fields = [];

    fieldsSnapshot.forEach((doc) => {
      const data = doc.data();
      fields.push({
        id: doc.id,
        name: data.name,
        type: data.type,
        size: data.size,
        sizeUnit: data.sizeUnit
      });
    });

    return { fields };
  } catch (error) {
    console.error('List fields error:', error);
    throw error;
  }
}

// Get plant creation options (all required lookups)
async function getPlantCreationOptions() {
  try {
    const [speciesLookups, statusLookups, healthLookups] = await Promise.all([
      getLookupsByCategory('plantSpecies'),
      getLookupsByCategory('plantStatus'),
      getLookupsByCategory('plantHealthStatus')
    ]);

    return {
      species: speciesLookups,
      status: statusLookups,
      health: healthLookups,
      message: 'Plant creation options retrieved successfully. Please provide species, status, and health lookup IDs when creating a plant.'
    };
  } catch (error) {
    console.error('Error getting plant creation options:', error);
    throw error;
  }
}

// Function to format entity listings for chat display
function formatEntityListForChat(entities, entityType) {
  if (!entities || entities.length === 0) {
    return `No ${entityType}s found in this farm.`;
  }

  let message = `Found ${entities.length} ${entityType}${entities.length > 1 ? 's' : ''}:\n\n`;

  entities.slice(0, 10).forEach((entity, index) => {
    switch (entityType) {
      case 'plant':
        message += `${index + 1}. ${entity.name || 'Unnamed Plant'}\n`;
        message += `   Species: ${entity.speciesName || entity.species || 'Unknown'}\n`;
        message += `   Status: ${entity.statusName || entity.status || 'Unknown'}\n`;
        message += `   Health: ${entity.healthName || entity.health || 'Unknown'}\n`;
        if (entity.variety) message += `   Variety: ${entity.variety}\n`;
        message += '\n';
        break;

      case 'animal':
        message += `${index + 1}. ${entity.name || 'Unnamed Animal'}\n`;
        message += `   Species: ${entity.species || 'Unknown'}\n`;
        message += `   Breed: ${entity.breed || 'Unknown'}\n`;
        message += `   Status: ${entity.status || 'Unknown'}\n`;
        message += '\n';
        break;

      case 'field':
        message += `${index + 1}. ${entity.name || 'Unnamed Field'}\n`;
        message += `   Type: ${entity.type || 'Unknown'}\n`;
        message += `   Size: ${entity.size || 0} ${entity.sizeUnit || 'units'}\n`;
        message += `   Status: ${entity.status || 'Unknown'}\n`;
        message += '\n';
        break;

      case 'garden':
        message += `${index + 1}. ${entity.name || 'Unnamed Garden'}\n`;
        message += `   Type: ${entity.gardenType || entity.type || 'Unknown'}\n`;
        message += `   Size: ${entity.size || 0} ${entity.sizeUnit || 'units'}\n`;
        message += `   Status: ${entity.status || 'Unknown'}\n`;
        message += '\n';
        break;

      case 'equipment':
        message += `${index + 1}. ${entity.name || 'Unnamed Equipment'}\n`;
        message += `   Type: ${entity.type || 'Unknown'}\n`;
        message += `   Status: ${entity.status || 'Unknown'}\n`;
        if (entity.manufacturer) message += `   Manufacturer: ${entity.manufacturer}\n`;
        message += '\n';
        break;

      case 'task':
        message += `${index + 1}. ${entity.title || 'Unnamed Task'}\n`;
        message += `   Status: ${entity.status || 'Unknown'}\n`;
        message += `   Priority: ${entity.priority || 'Unknown'}\n`;
        if (entity.dueDate) message += `   Due: ${new Date(entity.dueDate).toLocaleDateString()}\n`;
        message += '\n';
        break;
    }
  });

  if (entities.length > 10) {
    message += `... and ${entities.length - 10} more ${entityType}s.`;
  }

  return message;
}

module.exports = {
  runHealthCheck,
  getWateringSchedule,
  searchSimilarIssues,
  addPlant,
  addAnimal,
  addField,
  addGarden,
  addCropToField,
  getLookupsByCategory,
  getLookupById,
  getLookupsById,
  validateLookupId,
  getPlantCreationOptions,
  fetchPlants,
  fetchAnimals,
  fetchEquipment,
  fetchFields,
  fetchGardens,
  fetchTasks,
  listGardens,
  listFields,
  addPlantToGarden,
  formatEntityListForChat
};
