// routes/chat.js
const express = require('express');
const router = express.Router();
const { analyzeMessage } = require('../services/openai');
const admin = require('../services/firebase');
const { uploadImage } = require('../services/storage');
const {
  runHealthCheck,
  getWateringSchedule,
  searchSimilarIssues,
  addPlant,
  addAnimal,
  addField,
  addGarden,
  addCropToField,
  getLookupsByCategory,
  getLookupById,
  validateLookupId,
  getPlantCreationOptions,
  loadLookupData,
  fetchPlants,
  fetchAnimals,
  fetchEquipment,
  fetchFields,
  fetchGardens,
  fetchTasks,
  listGardens,
  listFields,
  addPlantToGarden,
  formatEntityListForChat
} = require('../services/actions');

async function saveChatMessage(userId, message) {
  const ref = admin.firestore().collection("users").doc(userId).collection("messages");
  await ref.add({
    ...message,
    timestamp: new Date()
  });
}
const app = express();
app.use(express.json({ limit: '10mb' })); // Important!
router.post('/analyze', async (req, res) => {
  console.log((req.body))
  try {
    const { text, userId, imageBase64, imageMimeType, imageName, language } = req.body;

    // Default language to English if not provided
    const userLanguage = language || 'english';

    let imageUrl = null;

    if (imageBase64 && imageMimeType && imageName) {
      const base64Data = imageBase64.split(';base64,').pop(); // remove data:image/jpeg;base64,
      const buffer = Buffer.from(base64Data, 'base64');

      imageUrl = await uploadImage(buffer, imageName, imageMimeType);
    }

    const aiResponse = await analyzeMessage(text, imageUrl, userLanguage);
    const suggestions = JSON.parse(aiResponse);

    // Save the message for history
    if (userId) {
      await saveChatMessage(userId, {
        text,
        imageUrl,
        language: userLanguage,
        aiResponse: suggestions
      });
    }

    res.json({ suggestions, language: userLanguage });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Analysis failed', message: err.message });
  }
});


router.post('/action', async (req, res) => {
  const { userId, farmId, action, entityData } = req.body;

  if (!action || !action.type) {
    return res.status(400).json({ error: 'Invalid action' });
  }

  try {
    let result;

    switch (action.type) {
      case 'health_check':
        result = await runHealthCheck(action.entityId);
        break;

      case 'view_schedule':
        result = await getWateringSchedule(action.entityId);
        break;

      case 'search_similar_issues':
        result = await searchSimilarIssues();
        break;
        
      case 'add_plant':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        result = await addPlant(farmId, { ...entityData, userId });
        break;
        
      case 'add_animal':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        result = await addAnimal(farmId, { ...entityData, userId });
        break;
        
      case 'add_field':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        result = await addField(farmId, { ...entityData, userId });
        break;
        
      case 'add_garden':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        result = await addGarden(farmId, { ...entityData, userId });
        break;
        
      case 'add_crop_to_field':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        if (!action.fieldId) return res.status(400).json({ error: 'Field ID is required' });
        result = await addCropToField(farmId, action.fieldId, entityData);
        break;
        
      case 'get_lookups':
        if (!action.category) return res.status(400).json({ error: 'Lookup category is required' });
        result = await getLookupsByCategory(action.category);
        break;

      case 'get_plant_creation_options':
        result = await getPlantCreationOptions();
        break;

      case 'load_lookup_data':
        result = await loadLookupData();
        break;

      case 'fetch_plants':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        const plantsData = await fetchPlants(farmId);
        result = {
          ...plantsData,
          chatMessage: formatEntityListForChat(plantsData.plants, 'plant')
        };
        break;

      case 'fetch_animals':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        const animalsData = await fetchAnimals(farmId);
        result = {
          ...animalsData,
          chatMessage: formatEntityListForChat(animalsData.animals, 'animal')
        };
        break;

      case 'fetch_equipment':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        const equipmentData = await fetchEquipment(farmId);
        result = {
          ...equipmentData,
          chatMessage: formatEntityListForChat(equipmentData.equipment, 'equipment')
        };
        break;

      case 'fetch_fields':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        const fieldsData = await fetchFields(farmId);
        result = {
          ...fieldsData,
          chatMessage: formatEntityListForChat(fieldsData.fields, 'field')
        };
        break;

      case 'fetch_gardens':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        const gardensData = await fetchGardens(farmId);
        result = {
          ...gardensData,
          chatMessage: formatEntityListForChat(gardensData.gardens, 'garden')
        };
        break;

      case 'fetch_tasks':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        const tasksData = await fetchTasks(farmId);
        result = {
          ...tasksData,
          chatMessage: formatEntityListForChat(tasksData.tasks, 'task')
        };
        break;

      case 'list_gardens':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        result = await listGardens(farmId);
        break;

      case 'list_fields':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        result = await listFields(farmId);
        break;

      case 'add_plant_to_garden':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        if (!action.gardenId && !entityData.gardenId) {
          // If no garden is specified, return list of gardens for selection
          const gardens = await listGardens(farmId);
          return res.json({
            requiresSelection: true,
            selectionType: 'garden',
            options: gardens.gardens,
            message: 'Please select a garden to add the plant to:',
            pendingAction: action,
            pendingEntityData: entityData
          });
        }
        const gardenId = action.gardenId || entityData.gardenId;
        result = await addPlantToGarden(farmId, { ...entityData, userId }, gardenId);
        break;

      case 'add_plant_to_field':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        if (!action.fieldId && !entityData.fieldId) {
          // If no field is specified, return list of fields for selection
          const fields = await listFields(farmId);
          return res.json({
            requiresSelection: true,
            selectionType: 'field',
            options: fields.fields,
            message: 'Please select a field to add the plant to:',
            pendingAction: action,
            pendingEntityData: entityData
          });
        }
        const fieldId = action.fieldId || entityData.fieldId;
        const plantDataWithField = { ...entityData, fieldId };
        result = await addPlant(farmId, { ...plantDataWithField, userId });
        break;

      default:
        result = { message: `No handler for action type: ${action.type}` };
    }

    res.json({ result });

  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Failed to handle action', message: err.message });
  }
});

// New endpoint to handle selection confirmations
router.post('/confirm-selection', async (req, res) => {
  const { userId, farmId, selectedId, pendingAction, pendingEntityData } = req.body;

  if (!selectedId || !pendingAction || !pendingEntityData) {
    return res.status(400).json({ error: 'Missing required selection data' });
  }

  try {
    let result;

    switch (pendingAction.type) {
      case 'add_plant_to_garden':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        result = await addPlantToGarden(farmId, { ...pendingEntityData, userId }, selectedId);
        break;

      case 'add_plant_to_field':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        // Update plant data with field location
        const plantDataWithField = { ...pendingEntityData, fieldId: selectedId };
        result = await addPlant(farmId, { ...plantDataWithField, userId });
        break;

      default:
        result = { message: `No handler for pending action type: ${pendingAction.type}` };
    }

    res.json({ result });

  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Failed to handle selection', message: err.message });
  }
});

module.exports = router;
