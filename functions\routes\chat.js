// routes/chat.js
const express = require('express');
const router = express.Router();
const { analyzeMessage } = require('../services/openai');
const admin = require('../services/firebase');
const { uploadImage } = require('../services/storage');
const { 
  runHealthCheck, 
  getWateringSchedule, 
  searchSimilarIssues,
  addPlant,
  addAnimal,
  addField,
  addGarden,
  addCropToField,
  getLookupsByCategory
} = require('../services/actions');

async function saveChatMessage(userId, message) {
  const ref = admin.firestore().collection("users").doc(userId).collection("messages");
  await ref.add({
    ...message,
    timestamp: new Date()
  });
}
const app = express();
app.use(express.json({ limit: '10mb' })); // Important!
router.post('/analyze', async (req, res) => {
  console.log((req.body))
  try {
    const { text, userId, imageBase64, imageMimeType, imageName } = req.body;

    let imageUrl = null;

    if (imageBase64 && imageMimeType && imageName) {
      const base64Data = imageBase64.split(';base64,').pop(); // remove data:image/jpeg;base64,
      const buffer = Buffer.from(base64Data, 'base64');

      imageUrl = await uploadImage(buffer, imageName, imageMimeType);
    }

    const aiResponse = await analyzeMessage(text, imageUrl);
    const suggestions = JSON.parse(aiResponse);

    // Save the message for history
    if (userId) {
      await saveChatMessage(userId, {
        text,
        imageUrl,
        aiResponse: suggestions
      });
    }

    res.json({ suggestions });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Analysis failed', message: err.message });
  }
});


router.post('/action', async (req, res) => {
  const { userId, farmId, action, entityData } = req.body;

  if (!action || !action.type) {
    return res.status(400).json({ error: 'Invalid action' });
  }

  try {
    let result;

    switch (action.type) {
      case 'health_check':
        result = await runHealthCheck(action.entityId);
        break;

      case 'view_schedule':
        result = await getWateringSchedule(action.entityId);
        break;

      case 'search_similar_issues':
        result = await searchSimilarIssues();
        break;
        
      case 'add_plant':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        result = await addPlant(farmId, { ...entityData, userId });
        break;
        
      case 'add_animal':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        result = await addAnimal(farmId, { ...entityData, userId });
        break;
        
      case 'add_field':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        result = await addField(farmId, { ...entityData, userId });
        break;
        
      case 'add_garden':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        result = await addGarden(farmId, { ...entityData, userId });
        break;
        
      case 'add_crop_to_field':
        if (!farmId) return res.status(400).json({ error: 'Farm ID is required' });
        if (!action.fieldId) return res.status(400).json({ error: 'Field ID is required' });
        result = await addCropToField(farmId, action.fieldId, entityData);
        break;
        
      case 'get_lookups':
        if (!action.category) return res.status(400).json({ error: 'Lookup category is required' });
        result = await getLookupsByCategory(action.category);
        break;

      default:
        result = { message: `No handler for action type: ${action.type}` };
    }

    res.json({ result });

  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Failed to handle action', message: err.message });
  }
});

module.exports = router;
